<template>
  <div class="contentBox">
    <div class="device-total">
      <div class="first">加工设备数量<span>2</span></div>
      <div class="second">累计加工时间<span>10h</span></div>
      <div class="third">累计切割米数<span>200.25</span></div>
    </div>
    <div class="device-list">
      <div class="device-info">
        <div class="image"><ElTableEmpty :empty-content="emptyContent" /></div>
        <div class="info">
          <div class="name" :class="{'running': status === '运行中'}">激光切割机A123<span><i />{{ status }}</span></div>
          <div class="duration">加工市场<span>5h</span></div>
          <div>开始加工<span>2025-12-25 10:34</span></div>
          <div>结束加工<span>2025-12-25 10:34</span></div>
          <div class="meters">切割米数<span>230.23</span></div>
          <div>平均电流<span>135A</span></div>
          <div>平均电压<span>160A</span></div>
        </div>
      </div>
      <div class="device-info">
        <div class="image"><ElTableEmpty :empty-content="emptyContent" /></div>
        <div class="info">
          <div class="name">激光切割机A123<span><i />{{ status }}</span></div>
          <div class="duration">加工市场<span>5h</span></div>
          <div>开始加工<span>2025-12-25 10:34</span></div>
          <div>结束加工<span>2025-12-25 10:34</span></div>
          <div class="meters">切割米数<span>230.23</span></div>
          <div>平均电流<span>135A</span></div>
          <div>平均电压<span>160A</span></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import ElTableEmpty from '@/components/ElTableEmpty/index.vue'
export default {
  name: 'SuggestDevice',
  components: {
    ElTableEmpty
  },
  data() {
    return {
      emptyContent: '暂无图片',
      status: '运行中'
    }
  },
  methods: {}
}
</script>
<style scoped lang="scss">
@import "~@/styles/mixin.scss";
.contentBox {
  height: 70vh;

  .device-total {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    div {
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 16px;
      border-radius: 4px;
      font-size: 14px;
      span {
        font-weight: bold;
        font-size: 16px;
        margin-left: 12px;
      }
    }
    div:nth-child(1) {
      margin-left: 8px;
      margin-right: 8px;
      background-color: rgba(0, 72, 152, .1);
      color: rgba(0, 72, 152, 1);
    }
    div:nth-child(2) {
      margin-left: 8px;
      margin-right: 8px;
      background-color: rgba(0, 141, 80, .1);
      color: rgba(0, 141, 80, 1);
    }
    div:nth-child(3) {
      margin-left: 8px;
      margin-right: 8px;
      background-color: rgba(33, 77, 194, .1);
      color: rgba(33, 77, 194, 1);
    }
  }
  .device-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    .device-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      .image {
        width: 50%;
        height: 270px;
        border-radius: 4px;
        background-color: #f5f5f5;
        margin-right: 10px;
      }
      .info {
        flex: 1;
        height: 270px;
        padding-left: 28px;
        background: linear-gradient( 134deg, rgba(41, 141, 255, .1) 0%, rgba(41,141,255,0) 100%);
        border-radius: 4px;
        padding-top: 24px;
        div {
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          margin-bottom: 16px;
          span {
            font-weight: bold;
            margin-left: 12px;
          }
        }
        div.name {
          font-weight: bold;
          font-size: 16px;
          color: #333333;
          margin-bottom: 26px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          span {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 4px 6px;
            color: rgba(142, 149, 170, 1);
            background-color: rgba(142, 149, 170, .1);
            border-radius: 8px;
            font-size: 12px;
            i {
              display: block;
              width: 5px;
              height: 5px;
              background-color: rgba(142, 149, 170, 1);
              border-radius: 50%;
              margin-right: 5px;
            }
          }
        }
        div.name.running {
          span {
            color: rgba(0, 141, 80, 1);
            background-color: rgba(0, 141, 80, .1);
            i {
              background-color: rgba(0, 141, 80, 1);
            }
          }
        }
        div.duration {
          span {
            color: #008D50;
          }
        }
        div.meters {
          span {
            color: #004898;
          }
        }
      }
    }
  }
}
</style>

{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue?vue&type=template&id=20c0ba33&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue", "mtime": 1758691302882}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}
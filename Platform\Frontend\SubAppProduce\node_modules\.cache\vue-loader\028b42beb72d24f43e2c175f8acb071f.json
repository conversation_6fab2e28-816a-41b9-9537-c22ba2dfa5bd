{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue?vue&type=template&id=20c0ba33&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue", "mtime": 1758691398558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}
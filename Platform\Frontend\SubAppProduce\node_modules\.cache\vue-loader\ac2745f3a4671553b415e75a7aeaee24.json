{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue?vue&type=template&id=20c0ba33&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue", "mtime": 1758691302882}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImNvbnRlbnRCb3giPgogIDxkaXYgY2xhc3M9ImRldmljZS10b3RhbCI+CiAgICA8ZGl2IGNsYXNzPSJmaXJzdCI+5Yqg5bel6K6+5aSH5pWw6YePPHNwYW4+Mjwvc3Bhbj48L2Rpdj4KICAgIDxkaXYgY2xhc3M9InNlY29uZCI+57Sv6K6h5Yqg5bel5pe26Ze0PHNwYW4+MTBoPC9zcGFuPjwvZGl2PgogICAgPGRpdiBjbGFzcz0idGhpcmQiPue0r+iuoeWIh+WJsuexs+aVsDxzcGFuPjIwMC4yNTwvc3Bhbj48L2Rpdj4KICA8L2Rpdj4KICA8ZGl2IGNsYXNzPSJkZXZpY2UtbGlzdCI+CiAgICA8ZGl2IGNsYXNzPSJkZXZpY2UtaW5mbyI+CiAgICAgIDxkaXYgY2xhc3M9ImltYWdlIj48RWxUYWJsZUVtcHR5IDplbXB0eS1jb250ZW50PSJlbXB0eUNvbnRlbnQiIC8+PC9kaXY+CiAgICAgIDxkaXYgY2xhc3M9ImluZm8iPgogICAgICAgIDxkaXYgY2xhc3M9Im5hbWUiIDpjbGFzcz0ieyAncnVubmluZyc6IHN0YXR1cyA9PT0gJ+i/kOihjOS4rScgfSI+5r+A5YWJ5YiH5Ymy5py6QTEyMzxzcGFuPjxpIC8+e3sgc3RhdHVzIH19PC9zcGFuPjwvZGl2PgogICAgICAgIDxkaXYgY2xhc3M9ImR1cmF0aW9uIj7liqDlt6XluILlnLo8c3Bhbj41aDwvc3Bhbj48L2Rpdj4KICAgICAgICA8ZGl2PuW8gOWni+WKoOW3pTxzcGFuPjIwMjUtMTItMjUgMTA6MzQ8L3NwYW4+PC9kaXY+CiAgICAgICAgPGRpdj7nu5PmnZ/liqDlt6U8c3Bhbj4yMDI1LTEyLTI1IDEwOjM0PC9zcGFuPjwvZGl2PgogICAgICAgIDxkaXYgY2xhc3M9Im1ldGVycyI+5YiH5Ymy57Gz5pWwPHNwYW4+MjMwLjIzPC9zcGFuPjwvZGl2PgogICAgICAgIDxkaXY+5bmz5Z2H55S15rWBPHNwYW4+MTM1QTwvc3Bhbj48L2Rpdj4KICAgICAgICA8ZGl2PuW5s+Wdh+eUteWOizxzcGFuPjE2MEE8L3NwYW4+PC9kaXY+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgPC9kaXY+CjwvZGl2Pgo="}, null]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue", "mtime": 1758691302882}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBFbFRhYmxlRW1wdHkgZnJvbSAnQC9jb21wb25lbnRzL0VsVGFibGVFbXB0eS9pbmRleC52dWUnCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnU3VnZ2VzdERldmljZScsCiAgY29tcG9uZW50czogewogICAgRWxUYWJsZUVtcHR5CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZW1wdHlDb250ZW50OiAn5pqC5peg5Zu+54mHJywKICAgICAgc3RhdHVzOiAn6L+Q6KGM5LitJwogICAgfQogIH0sCiAgbWV0aG9kczoge30KfQo="}, {"version": 3, "sources": ["suggestDevice.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "suggestDevice.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\n  <div class=\"contentBox\">\n    <div class=\"device-total\">\n      <div class=\"first\">加工设备数量<span>2</span></div>\n      <div class=\"second\">累计加工时间<span>10h</span></div>\n      <div class=\"third\">累计切割米数<span>200.25</span></div>\n    </div>\n    <div class=\"device-list\">\n      <div class=\"device-info\">\n        <div class=\"image\"><ElTableEmpty :empty-content=\"emptyContent\" /></div>\n        <div class=\"info\">\n          <div class=\"name\" :class=\"{ 'running': status === '运行中' }\">激光切割机A123<span><i />{{ status }}</span></div>\n          <div class=\"duration\">加工市场<span>5h</span></div>\n          <div>开始加工<span>2025-12-25 10:34</span></div>\n          <div>结束加工<span>2025-12-25 10:34</span></div>\n          <div class=\"meters\">切割米数<span>230.23</span></div>\n          <div>平均电流<span>135A</span></div>\n          <div>平均电压<span>160A</span></div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nimport ElTableEmpty from '@/components/ElTableEmpty/index.vue'\nexport default {\n  name: 'SuggestDevice',\n  components: {\n    ElTableEmpty\n  },\n  data() {\n    return {\n      emptyContent: '暂无图片',\n      status: '运行中'\n    }\n  },\n  methods: {}\n}\n</script>\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.contentBox {\n  height: 70vh;\n\n  .device-total {\n    display: flex;\n    justify-content: center;\n    margin-bottom: 20px;\n    div {\n      height: 36px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      padding: 0 16px;\n      border-radius: 4px;\n      font-size: 14px;\n      span {\n        font-weight: bold;\n        font-size: 16px;\n        margin-left: 12px;\n      }\n    }\n    div:nth-child(1) {\n      margin-left: 8px;\n      margin-right: 8px;\n      background-color: rgba(0, 72, 152, .1);\n      color: rgba(0, 72, 152, 1);\n    }\n    div:nth-child(2) {\n      margin-left: 8px;\n      margin-right: 8px;\n      background-color: rgba(0, 141, 80, .1);\n      color: rgba(0, 141, 80, 1);\n    }\n    div:nth-child(3) {\n      margin-left: 8px;\n      margin-right: 8px;\n      background-color: rgba(33, 77, 194, .1);\n      color: rgba(33, 77, 194, 1);\n    }\n  }\n  .device-list {\n    width: 100%;\n    display: flex;\n    flex-direction: column;\n    .device-info {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16px;\n      .image {\n        width: 50%;\n        height: 270px;\n        border-radius: 4px;\n        background-color: #f5f5f5;\n        margin-right: 10px;\n      }\n      .info {\n        flex: 1;\n        height: 270px;\n        padding-left: 28px;\n        background: linear-gradient( 134deg, rgba(41, 141, 255, .1) 0%, rgba(41,141,255,0) 100%);\n        border-radius: 4px;\n        padding-top: 25px;\n        div {\n          font-weight: 400;\n          font-size: 14px;\n          color: #666666;\n          margin-bottom: 16px;\n          span {\n            font-weight: bold;\n            margin-left: 12px;\n          }\n        }\n        div.name {\n          font-weight: bold;\n          font-size: 16px;\n          color: #333333;\n          margin-bottom: 26px;\n          display: flex;\n          justify-content: flex-start;\n          align-items: center;\n          span {\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            padding: 4px 6px;\n            color: rgba(142, 149, 170, 1);\n            background-color: rgba(142, 149, 170, .1);\n            border-radius: 8px;\n            font-size: 12px;\n            i {\n              display: block;\n              width: 5px;\n              height: 5px;\n              background-color: rgba(142, 149, 170, 1);\n              border-radius: 50%;\n              margin-right: 5px;\n            }\n          }\n        }\n        div.duration {\n          span {\n            color: #008D50;\n          }\n        }\n        div.meters {\n          span {\n            color: #004898;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue?vue&type=style&index=0&id=20c0ba33&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue", "mtime": 1758691398558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["suggestDevice.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "suggestDevice.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\n  <div class=\"contentBox\">\n    <div class=\"device-total\">\n      <div class=\"first\">加工设备数量<span>2</span></div>\n      <div class=\"second\">累计加工时间<span>10h</span></div>\n      <div class=\"third\">累计切割米数<span>200.25</span></div>\n    </div>\n    <div class=\"device-list\">\n      <div class=\"device-info\">\n        <div class=\"image\"><ElTableEmpty :empty-content=\"emptyContent\" /></div>\n        <div class=\"info\">\n          <div :class=\"{'name': true, 'running': status === '运行中'}\">激光切割机A123<span><i />{{ status }}</span></div>\n          <div class=\"duration\">加工市场<span>5h</span></div>\n          <div>开始加工<span>2025-12-25 10:34</span></div>\n          <div>结束加工<span>2025-12-25 10:34</span></div>\n          <div class=\"meters\">切割米数<span>230.23</span></div>\n          <div>平均电流<span>135A</span></div>\n          <div>平均电压<span>160A</span></div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nimport ElTableEmpty from '@/components/ElTableEmpty/index.vue'\nexport default {\n  name: 'SuggestDevice',\n  components: {\n    ElTableEmpty\n  },\n  data() {\n    return {\n      emptyContent: '暂无图片',\n      status: '运行中'\n    }\n  },\n  methods: {}\n}\n</script>\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.contentBox {\n  height: 70vh;\n\n  .device-total {\n    display: flex;\n    justify-content: center;\n    margin-bottom: 20px;\n    div {\n      height: 36px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      padding: 0 16px;\n      border-radius: 4px;\n      font-size: 14px;\n      span {\n        font-weight: bold;\n        font-size: 16px;\n        margin-left: 12px;\n      }\n    }\n    div:nth-child(1) {\n      margin-left: 8px;\n      margin-right: 8px;\n      background-color: rgba(0, 72, 152, .1);\n      color: rgba(0, 72, 152, 1);\n    }\n    div:nth-child(2) {\n      margin-left: 8px;\n      margin-right: 8px;\n      background-color: rgba(0, 141, 80, .1);\n      color: rgba(0, 141, 80, 1);\n    }\n    div:nth-child(3) {\n      margin-left: 8px;\n      margin-right: 8px;\n      background-color: rgba(33, 77, 194, .1);\n      color: rgba(33, 77, 194, 1);\n    }\n  }\n  .device-list {\n    width: 100%;\n    display: flex;\n    flex-direction: column;\n    .device-info {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16px;\n      .image {\n        width: 50%;\n        height: 270px;\n        border-radius: 4px;\n        background-color: #f5f5f5;\n        margin-right: 10px;\n      }\n      .info {\n        flex: 1;\n        height: 270px;\n        padding-left: 28px;\n        background: linear-gradient( 134deg, rgba(41, 141, 255, .1) 0%, rgba(41,141,255,0) 100%);\n        border-radius: 4px;\n        padding-top: 25px;\n        div {\n          font-weight: 400;\n          font-size: 14px;\n          color: #666666;\n          margin-bottom: 16px;\n          span {\n            font-weight: bold;\n            margin-left: 12px;\n          }\n        }\n        div.name {\n          font-weight: bold;\n          font-size: 16px;\n          color: #333333;\n          margin-bottom: 26px;\n          display: flex;\n          justify-content: flex-start;\n          align-items: center;\n          span {\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            padding: 4px 6px;\n            color: rgba(142, 149, 170, 1);\n            background-color: rgba(142, 149, 170, .1);\n            border-radius: 8px;\n            font-size: 12px;\n            i {\n              display: block;\n              width: 5px;\n              height: 5px;\n              background-color: rgba(142, 149, 170, 1);\n              border-radius: 50%;\n              margin-right: 5px;\n            }\n          }\n        }\n        div.duration {\n          span {\n            color: #008D50;\n          }\n        }\n        div.meters {\n          span {\n            color: #004898;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}
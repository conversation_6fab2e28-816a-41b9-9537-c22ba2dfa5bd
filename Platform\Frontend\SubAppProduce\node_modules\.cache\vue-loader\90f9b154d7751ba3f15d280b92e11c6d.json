{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue?vue&type=style&index=0&id=20c0ba33&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue", "mtime": 1758693133510}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["suggestDevice.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "suggestDevice.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\n  <div class=\"contentBox\">\n    <div class=\"device-total\">\n      <div class=\"first\">加工设备数量<span>2</span></div>\n      <div class=\"second\">累计加工时间<span>10h</span></div>\n      <div class=\"third\">累计切割米数<span>200.25</span></div>\n    </div>\n    <div class=\"device-list\">\n      <div class=\"device-info\">\n        <div class=\"image\"><ElTableEmpty :empty-content=\"emptyContent\" /></div>\n        <div class=\"info\">\n          <div class=\"name\" :class=\"{'running': status === '运行中'}\">激光切割机A123<span><i />{{ status }}</span></div>\n          <div class=\"duration\">加工市场<span>5h</span></div>\n          <div>开始加工<span>2025-12-25 10:34</span></div>\n          <div>结束加工<span>2025-12-25 10:34</span></div>\n          <div class=\"meters\">切割米数<span>230.23</span></div>\n          <div>平均电流<span>135A</span></div>\n          <div>平均电压<span>160A</span></div>\n        </div>\n      </div>\n      <div class=\"device-info\">\n        <div class=\"image\"><ElTableEmpty :empty-content=\"emptyContent\" /></div>\n        <div class=\"info\">\n          <div class=\"name\">激光切割机A123<span><i />休息中</span></div>\n          <div class=\"duration\">加工市场<span>5h</span></div>\n          <div>开始加工<span>2025-12-25 10:34</span></div>\n          <div>结束加工<span>2025-12-25 10:34</span></div>\n          <div class=\"meters\">切割米数<span>230.23</span></div>\n          <div>平均电流<span>135A</span></div>\n          <div>平均电压<span>160A</span></div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nimport ElTableEmpty from '@/components/ElTableEmpty/index.vue'\nexport default {\n  name: 'SuggestDevice',\n  components: {\n    ElTableEmpty\n  },\n  data() {\n    return {\n      emptyContent: '暂无图片',\n      status: '运行中'\n    }\n  },\n  methods: {}\n}\n</script>\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.contentBox {\n  height: 800px;\n\n  .device-total {\n    display: flex;\n    justify-content: center;\n    margin-bottom: 20px;\n    div {\n      height: 36px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      padding: 0 16px;\n      border-radius: 4px;\n      font-size: 14px;\n      span {\n        font-weight: bold;\n        font-size: 16px;\n        margin-left: 12px;\n      }\n    }\n    div:nth-child(1) {\n      margin-left: 8px;\n      margin-right: 8px;\n      background-color: rgba(0, 72, 152, .1);\n      color: rgba(0, 72, 152, 1);\n    }\n    div:nth-child(2) {\n      margin-left: 8px;\n      margin-right: 8px;\n      background-color: rgba(0, 141, 80, .1);\n      color: rgba(0, 141, 80, 1);\n    }\n    div:nth-child(3) {\n      margin-left: 8px;\n      margin-right: 8px;\n      background-color: rgba(33, 77, 194, .1);\n      color: rgba(33, 77, 194, 1);\n    }\n  }\n  .device-list {\n    width: 100%;\n    display: flex;\n    flex-direction: column;\n    .device-info {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16px;\n      .image {\n        width: 50%;\n        height: 270px;\n        border-radius: 4px;\n        background-color: #f5f5f5;\n        margin-right: 10px;\n      }\n      .info {\n        flex: 1;\n        height: 270px;\n        padding-left: 28px;\n        background: linear-gradient( 134deg, rgba(41, 141, 255, .1) 0%, rgba(41,141,255,0) 100%);\n        border-radius: 4px;\n        padding-top: 24px;\n        div {\n          font-weight: 400;\n          font-size: 14px;\n          color: #666666;\n          margin-bottom: 16px;\n          span {\n            font-weight: bold;\n            margin-left: 12px;\n          }\n        }\n        div.name {\n          font-weight: bold;\n          font-size: 16px;\n          color: #333333;\n          margin-bottom: 26px;\n          display: flex;\n          justify-content: flex-start;\n          align-items: center;\n          span {\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            padding: 4px 6px;\n            color: rgba(142, 149, 170, 1);\n            background-color: rgba(142, 149, 170, .1);\n            border-radius: 8px;\n            font-size: 12px;\n            i {\n              display: block;\n              width: 5px;\n              height: 5px;\n              background-color: rgba(142, 149, 170, 1);\n              border-radius: 50%;\n              margin-right: 5px;\n            }\n          }\n        }\n        div.name.running {\n          span {\n            color: rgba(0, 141, 80, 1);\n            background-color: rgba(0, 141, 80, .1);\n            i {\n              background-color: rgba(0, 141, 80, 1);\n            }\n          }\n        }\n        div.duration {\n          span {\n            color: #008D50;\n          }\n        }\n        div.meters {\n          span {\n            color: #004898;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}